import multer from "multer";

export const fileValidations={
    Image :['jpg', 'jpeg', 'png', 'gif'],
    Document : ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx']
}
export const uploadCloudFile = (extensions = fileValidations.Image) => {
  
  const storage = multer.diskStorage({});

  const fileFilter = (req, file, cb) => {
    const fileExtension = file.originalname.split('.').pop().toLowerCase();
    if (extensions.includes(fileExtension)) {
      cb(null, true);
    } else {
      cb("Invalid Format!!", false);
    }
  };

  return multer({ storage, fileFilter });
};