# Database Configuration
DATABASE_URL="sqlserver://server:port;database=dbname;user=username;password=password;trustServerCertificate=true"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-here"
JWT_EXPIRES_IN="7d"

# Password Hashing (ASP.NET Core Identity)
# No additional configuration needed - uses built-in ASP.NET Core Identity algorithm

# Server Configuration
PORT=3000
NODE_ENV=development

# Email Configuration (for future use)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=465
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# File Upload Configuration (for future use)
CLOUD_NAME=your-cloudinary-cloud-name
CLOUD_API_KEY=your-cloudinary-api-key
CLOUD_API_SEC=your-cloudinary-api-secret

# Encryption (for future use)
SECRET_KEY=your-encryption-secret-key
